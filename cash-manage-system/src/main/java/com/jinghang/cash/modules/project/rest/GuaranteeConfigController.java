/*
*  Copyright 2019-2025 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.jinghang.cash.modules.project.rest;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jinghang.cash.api.dto.GuaranteeConfigDto;
import com.jinghang.cash.modules.manage.RestResult;
import com.jinghang.cash.modules.project.domain.GuaranteeConfig;
import com.jinghang.cash.modules.project.domain.dto.GuaranteeConfigQueryCriteria;
import com.jinghang.cash.modules.project.service.GuaranteeConfigService;
import com.jinghang.cash.utils.PageResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
* <AUTHOR>
* @date 2025-08-22
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "融担方信息")
@RequestMapping("/api/guaranteeConfig")
public class GuaranteeConfigController {

    private final GuaranteeConfigService guaranteeConfigService;

    @GetMapping("/info")
    @ApiOperation("查询融担方详情信息")
    public RestResult<GuaranteeConfig> queryGuaranteeConfigByid(@RequestParam String id){
        return RestResult.success(guaranteeConfigService.selectByid(id));
    }

    @GetMapping("/queryGuaranteeConfig")
    @ApiOperation("查询融担方信息")
    public RestResult<PageResult<GuaranteeConfig>> queryGuaranteeConfig(GuaranteeConfigQueryCriteria criteria){
        Page<Object> page = new Page<>(criteria.getPageNum(), criteria.getPageSize());
        return RestResult.success(guaranteeConfigService.selectAll(criteria,page));
    }

    @PostMapping("/createGuaranteeConfig")
    @ApiOperation("新增融担方信息")
    public RestResult<Object> createGuaranteeConfig(@Validated @RequestBody GuaranteeConfigDto resources){
        guaranteeConfigService.create(resources);
        return RestResult.success();
    }

    @PostMapping("/updateGuaranteeConfig")
    @ApiOperation("修改融担方信息")
    public RestResult<Object> updateGuaranteeConfig(@Validated @RequestBody GuaranteeConfigDto resources){
        guaranteeConfigService.update(resources);
        return RestResult.success();
    }

    @PostMapping("/enable")
    @ApiOperation("禁用启用")
    public RestResult<Object> enable(@Validated @RequestBody GuaranteeConfigDto resources){
        guaranteeConfigService.enable(resources);
        return RestResult.success();
    }

}