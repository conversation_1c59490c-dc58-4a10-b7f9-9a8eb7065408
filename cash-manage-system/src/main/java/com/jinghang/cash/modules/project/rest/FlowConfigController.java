/*
*  Copyright 2019-2025 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.jinghang.cash.modules.project.rest;

import com.jinghang.cash.api.dto.FlowConfigDto;
import com.jinghang.cash.modules.manage.RestResult;
import com.jinghang.cash.modules.project.domain.FlowConfig;
import com.jinghang.cash.modules.project.domain.dto.FlowConfigQueryCriteria;
import com.jinghang.cash.modules.project.service.FlowConfigService;
import com.jinghang.cash.utils.PageResult;
import com.jinghang.ppd.api.dto.route.EnableFlowConfigDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
* <AUTHOR>
* @date 2025-08-22
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "资产控制")
@RequestMapping("/api/flowConfig")
public class FlowConfigController {

    private final FlowConfigService flowConfigService;

    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('flowConfig:export')")
    public void exportFlowConfig(HttpServletResponse response, FlowConfigQueryCriteria criteria) throws IOException {
        flowConfigService.download(flowConfigService.queryAll(criteria), response);
    }

    @GetMapping("/queryPage")
    @ApiOperation("查询资产列表分页")
    @PreAuthorize("@el.check('flowConfig:list')")
    public RestResult<PageResult<FlowConfig>> queryFlowConfig(FlowConfigQueryCriteria criteria){
        return RestResult.success(flowConfigService.queryAllPage(criteria));
    }

    @GetMapping("/info")
    @PreAuthorize("@el.check('flowConfig:queryFlowConfigInfo')")
    public RestResult<FlowConfig> queryFlowConfigInfo(@RequestParam String id){
        return RestResult.success(flowConfigService.queryFlowConfigInfo(id));
    }


    @PostMapping("/create")
    @ApiOperation("新增资产控制")
    @PreAuthorize("@el.check('flowConfig:add')")
    public RestResult<Object> createFlowConfig(@Validated @RequestBody FlowConfigDto resources){
        flowConfigService.create(resources);
        return RestResult.success();
    }

    @PostMapping("/update")
    @ApiOperation("修改资产控制")
    @PreAuthorize("@el.check('flowConfig:edit')")
    public RestResult<Object> updateFlowConfig(@Validated @RequestBody FlowConfigDto resources){
        flowConfigService.update(resources);
        return RestResult.success();
    }

    @PostMapping("/enable")
    @ApiOperation("启用禁用")
    @PreAuthorize("@el.check('flowConfig:enable')")
    public RestResult<Object> updateFlowConfig(@Validated @RequestBody EnableFlowConfigDTO resources){
        flowConfigService.enable(resources);
        return RestResult.success();
    }

    @PostMapping("/del")
    @ApiOperation("删除资产控制")
    @PreAuthorize("@el.check('flowConfig:del')")
    public RestResult<Object> deleteFlowConfig(@ApiParam(value = "传ID数组[]") @RequestBody List<String> ids) {
        flowConfigService.deleteAll(ids);
        return RestResult.success(HttpStatus.OK);
    }
}