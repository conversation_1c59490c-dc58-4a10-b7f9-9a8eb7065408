/*
*  Copyright 2019-2025 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.jinghang.cash.modules.project.rest;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jinghang.cash.annotation.Log;
import com.jinghang.cash.api.dto.CapitalConfigDto;
import com.jinghang.cash.modules.manage.RestResult;
import com.jinghang.cash.modules.project.domain.CapitalConfig;
import com.jinghang.cash.modules.project.domain.dto.CapitalConfigQueryCriteria;
import com.jinghang.cash.modules.project.service.CapitalConfigService;
import com.jinghang.cash.utils.PageResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
* <AUTHOR>
* @date 2025-08-22
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "资方信息")
@RequestMapping("/api/capitalConfig")
public class CapitalConfigController {

    private final CapitalConfigService capitalConfigService;


    @GetMapping("/queryCapitalConfigByid")
    @ApiOperation("查询融担方详情信息")
    public RestResult<CapitalConfig> queryGuaranteeConfigByid(@RequestParam String id){
        return RestResult.success(capitalConfigService.selectByid(id));
    }


    @GetMapping("/queryCapitalConfig")
    @ApiOperation("查询资方信息")
    public RestResult<PageResult<CapitalConfig>> queryCapitalConfig(CapitalConfigQueryCriteria criteria){
        Page<Object> page = new Page<>(criteria.getPageNum(), criteria.getPageSize());
        return RestResult.success(capitalConfigService.selectAll(criteria,page));
    }

    @PostMapping("/createCapitalConfig")
    @Log("新增资方信息")
    @ApiOperation("新增资方信息")
    public RestResult<Object> createCapitalConfig(@Validated @RequestBody CapitalConfigDto resources){
        capitalConfigService.create(resources);
        return RestResult.success();
    }

    @PostMapping("/updateCapitalConfig")
    @Log("修改资方信息")
    @ApiOperation("修改资方信息")
    public RestResult<Object> updateCapitalConfig(@Validated @RequestBody CapitalConfigDto resources){
        capitalConfigService.update(resources);
        return RestResult.success();
    }

    @PostMapping("/enable")
    @Log("禁用启用")
    @ApiOperation("禁用启用")
    public RestResult<Object> enable(@Validated @RequestBody CapitalConfigDto resources){
        capitalConfigService.enable(resources);
        return RestResult.success();
    }

    @DeleteMapping
    @Log("删除资方信息")
    @ApiOperation("删除资方信息")
    public RestResult<Object> deleteCapitalConfig(@ApiParam(value = "传ID数组[]") @RequestBody List<String> ids) {
        capitalConfigService.deleteAll(ids);
        return RestResult.success();
    }
}