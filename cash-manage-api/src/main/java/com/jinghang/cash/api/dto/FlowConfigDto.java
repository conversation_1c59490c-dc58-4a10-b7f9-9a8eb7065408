/*
*  Copyright 2019-2025 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.jinghang.cash.api.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
* @description /
* <AUTHOR>
* @date 2025-08-22
**/
@Data
public class FlowConfigDto implements Serializable {

    ////@ApiModelProperty(value = "主键")
    private String id;

    //@ApiModelProperty(value = "流量渠道")
    private String flowChannel;

    //@ApiModelProperty(value = "启用状态")
    private String enabled;

    //@ApiModelProperty(value = "备注")
    private String remark;

    //@ApiModelProperty(value = "乐观锁")
    private String revision;

    //@ApiModelProperty(value = "创建人")
    private String createdBy;

    //@ApiModelProperty(value = "创建时间")
    private LocalDateTime createdTime;

    //@ApiModelProperty(value = "更新人")
    private String updatedBy;

    //@ApiModelProperty(value = "更新时间")
    private LocalDateTime updatedTime;

    //@ApiModelProperty(value = "资产方简称")
    private String flowNameShort;

    //@ApiModelProperty(value = "资产方主体全称")
    private String flowName;

    //@ApiModelProperty(value = "资方简介")
    private String flowDesc;

    //@ApiModelProperty(value = "联系人")
    private String contactPerson;

    //@ApiModelProperty(value = "联系电话")
    //@Pattern(regexp = "^1\\d{10}$", message = "手机号格式错误")
    //@Size(min = 11, max = 11, message = "手机号长度必须为11位")
    private String contactPhone;

    //@ApiModelProperty(value = "邮箱地址")
    private String emailAddress;

    //@ApiModelProperty(value = "主要产品")
    private String mainProd;

    /**
     * 开始日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate startDate;

}
