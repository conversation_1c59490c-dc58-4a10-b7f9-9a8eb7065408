<template>
    <div class="app-container">
      <el-collapse :value ="activeNames">
        <el-collapse-item title="项目详情" name="1">
          <el-form ref="form" :model="form" :rules="rules" size="small" label-width="120px">
            <el-row :gutter="24">
              <el-col :span="8">
                <el-form-item label="项目编码" prop="projectCode">
                  <el-input v-model="form.projectCode" style="width: 280px;" disabled />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="项目名称" prop="projectName">
                  <el-input v-model="form.projectName" style="width: 280px;" :disabled="type === 'detail'"/>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="项目状态" prop="enabled">
                  <el-select v-model="form.enabled" clearable size="small" placeholder="请选择项目状态" class="filter-item" style="width: 280px" :disabled="type === 'detail'" >
                    <el-option v-for="item in ableStatusExt" :key="item.label" :label="item.value" :value="item.label" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24">
              <el-col :span="8">
                <el-form-item label="渠道方" prop="mainProd">
                  <el-select v-model="form.flowChannel" clearable size="small" placeholder="请选择资产方" class="filter-item" style="width: 280px;" :disabled="type === 'detail'">
                    <el-option v-for="item in assetList" :key="item.flowChannel" :label="item.flowNameShort" :value="item.flowChannel" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="融担方" prop="contactPerson">
                  <el-select v-model="form.guaranteeCode" clearable size="small" placeholder="请选择融担方" class="filter-item" style="width: 280px;" :disabled="type === 'detail'">
                    <el-option v-for="item in guaranteeList" :key="item.guaranteeCode" :label="item.guaranteeNameShort" :value="item.guaranteeCode" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="资金方" prop="contactPhone">
                  <el-select v-model="form.capitalChannel" clearable size="small" placeholder="请选择资金方" class="filter-item" style="width: 280px;" :disabled="type === 'detail'">
                    <el-option v-for="item in fundList" :key="item.bankChannel" :label="item.capitalNameShort" :value="item.bankChannel" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24">
              <el-col :span="8">
                <el-form-item label="项目类型" prop="projectTypeCode">
                  <el-select v-model="form.projectTypeCode" clearable size="small" placeholder="请选择项目类型" class="filter-item" style="width: 280px;" :disabled="type === 'detail'">
                    <el-option v-for="item in projectType" :key="item.label" :label="item.value" :value="item.label" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="项目开始日期" prop="startDate">
                  <el-date-picker
                    v-model="form.startDate"
                    align="right"
                    type="date"
                    placeholder="选择日期"
                    style="width: 280px;"
                    :disabled="type === 'detail'">
                  </el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="项目结束日期" prop="endDate">
                  <el-date-picker
                    v-model="form.endDate"
                    align="right"
                    type="date"
                    placeholder="选择日期"
                    style="width: 280px;"
                    :disabled="type === 'detail'">
                  </el-date-picker>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-collapse-item>
      </el-collapse>
      <el-tabs v-model="activeName">
        <el-tab-pane label="产品要素" name="first">
          <el-collapse :value="activeNames">
            <el-collapse-item title="基本要素" name="2">
              <el-form ref="form" :model="form" :rules="rules" size="small" label-width="120px">
                <el-row :gutter="24">
                  <el-col :span="8">
                    <el-form-item label="产品要素编码" prop="id">
                      <el-input v-model="form.id" style="width: 280px;" :disabled="type === 'detail'"/>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="产品说明" prop="flowNameShort">
                      <el-input v-model="form.flowNameShort" style="width: 280px;" :disabled="type === 'detail'"/>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="对客利率" prop="customerInterestRate">
                      <el-radio-group v-model="form.customerInterestRate" :disabled="type === 'detail'">
                        <el-radio :label="23.99">23.99%</el-radio>
                        <el-radio :label="35.99">35.99%</el-radio>
                      </el-radio-group>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="24">
                  <el-col :span="8">
                    <el-form-item label="对资利率" prop="fundingInterestRate">
                      <el-radio-group v-model="form.fundingInterestRate" :disabled="type === 'detail'">
                        <el-radio :label="23.99">23.99%</el-radio>
                        <el-radio :label="35.99">35.99%</el-radio>
                      </el-radio-group>
                    </el-form-item>
                  </el-col>
                  <el-col :span="16">
                    <el-form-item label="借款期限" prop="loanTerms">
                      <el-checkbox-group v-model="form.loanTerms" @change="handleCheckedCitiesChange" :disabled="type === 'detail'">
                        <el-checkbox v-for="terms in loanTermsOptions" :label="terms" :key="terms">{{terms}}</el-checkbox>
                      </el-checkbox-group>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="24">
                  <el-col :span="8">
                    <el-form-item label="对资授信黑暗期" prop="fundingCreditDarkHours">
                      <el-time-picker
                        is-range
                        v-model="form.fundingCreditDarkHours"
                        range-separator="-"
                        start-placeholder="开始时间"
                        end-placeholder="结束时间"
                        placeholder="选择时间范围" style="width: 280px;"
                        :disabled="type === 'detail'">
                      </el-time-picker>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="对资用信黑暗期" prop="fundingLoanDarkHours">
                      <el-time-picker
                        is-range
                        v-model="form.fundingLoanDarkHours"
                        range-separator="-"
                        start-placeholder="开始时间"
                        end-placeholder="结束时间"
                        placeholder="选择时间范围" style="width: 280px;"
                        :disabled="type === 'detail'">
                      </el-time-picker>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="对资还款黑暗期" prop="fundingRepayDarkHours">
                      <el-time-picker
                        is-range
                        v-model="form.fundingRepayDarkHours"
                        range-separator="-"
                        start-placeholder="开始时间"
                        end-placeholder="结束时间"
                        placeholder="选择时间范围" style="width: 280px;"
                        :disabled="type === 'detail'">
                      </el-time-picker>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="24">
                  <el-col :span="8">
                    <el-form-item label="对客授信黑暗期" prop="creditDarkHours">
                      <el-time-picker
                        is-range
                        v-model="form.creditDarkHours"
                        range-separator="-"
                        start-placeholder="开始时间"
                        end-placeholder="结束时间"
                        placeholder="选择时间范围" style="width: 280px;"
                        :disabled="type === 'detail'">
                      </el-time-picker>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="对客用信黑暗期" prop="loanDarkHours">
                      <el-time-picker
                        is-range
                        v-model="form.loanDarkHours"
                        range-separator="-"
                        start-placeholder="开始时间"
                        end-placeholder="结束时间"
                        placeholder="选择时间范围" style="width: 280px;"
                        :disabled="type === 'detail'">
                      </el-time-picker>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="对客还款黑暗期" prop="repayDarkHours">
                      <el-time-picker
                        is-range
                        v-model="form.repayDarkHours"
                        range-separator="-"
                        start-placeholder="开始时间"
                        end-placeholder="结束时间"
                        placeholder="选择时间范围" style="width: 280px;"
                        :disabled="type === 'detail'">
                      </el-time-picker>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="24">
                  <el-col :span="8">
                    <el-form-item label="签章渠道" prop="signChannel">
                      <el-select v-model="form.signChannel" clearable size="small" placeholder="请选择资产方" class="filter-item" style="width: 280px;" :disabled="type === 'detail'">
                        <el-option v-for="item in assetList" :key="item.flowChannel" :label="item.flowNameShort" :value="item.flowChannel" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="短信发送方" prop="overdueSmsSender">
                      <el-select v-model="form.overdueSmsSender" clearable size="small" placeholder="请选择资产方" class="filter-item" style="width: 280px;" :disabled="type === 'detail'">
                        <el-option v-for="item in assetList" :key="item.flowChannel" :label="item.flowNameShort" :value="item.flowChannel" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
            </el-collapse-item>
            <el-collapse-item title="授信" name="3">
              <el-form ref="form" :model="form" :rules="rules" size="small" label-width="120px">
                <el-row :gutter="24">
                  <el-col :span="8">
                    <el-form-item label="风控模型渠道" prop="riskModelChannel">
                      <el-select v-model="form.riskModelChannel" clearable size="small" placeholder="请选择资产方" class="filter-item" style="width: 280px;" :disabled="type === 'detail'">
                        <el-option v-for="item in assetList" :key="item.flowChannel" :label="item.flowNameShort" :value="item.flowChannel" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="年龄计算方式" prop="flowNameShort">
                      <el-select v-model="form.flowChannel" clearable size="small" placeholder="请选择资产方" class="filter-item" style="width: 280px;" :disabled="type === 'detail'">
                        <el-option v-for="item in assetList" :key="item.flowChannel" :label="item.flowNameShort" :value="item.flowChannel" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="日授信限额(万元)" prop="dailyCreditLimit">
                      <el-input v-model="form.dailyCreditLimit" style="width: 280px;" :disabled="type === 'detail'"/>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="24">
                  <el-col :span="8">
                    <el-form-item label="年龄范围(岁)" prop="ageRange">
                      <el-select v-model="form.ageRange" clearable size="small" placeholder="请选择资产方" class="filter-item" style="width: 280px;" :disabled="type === 'detail'">
                        <el-option v-for="item in assetList" :key="item.flowChannel" :label="item.flowNameShort" :value="item.flowChannel" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="征信查询方" prop="creditQueryParty">
                      <el-select v-model="form.creditQueryParty" clearable size="small" placeholder="请选择资产方" class="filter-item" style="width: 280px;" :disabled="type === 'detail'">
                        <el-option v-for="item in assetList" :key="item.flowChannel" :label="item.flowNameShort" :value="item.flowChannel" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="征信上报方" prop="creditReportSender">
                      <el-select v-model="form.creditReportSender" clearable size="small" placeholder="请选择资产方" class="filter-item" style="width: 280px;" :disabled="type === 'detail'">
                        <el-option v-for="item in assetList" :key="item.flowChannel" :label="item.flowNameShort" :value="item.flowChannel" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
            </el-collapse-item>
            <el-collapse-item title="放款" name="4">
              <el-form ref="form" :model="form" :rules="rules" size="small" label-width="120px">
                <el-row :gutter="24">
                  <el-col :span="8">
                    <el-form-item label="放款渠道" prop="loanPaymentChannel">
                      <el-select v-model="form.loanPaymentChannel" clearable size="small" placeholder="请选择资产方" class="filter-item" style="width: 280px;" :disabled="type === 'detail'">
                        <el-option v-for="item in assetList" :key="item.flowChannel" :label="item.flowNameShort" :value="item.flowChannel" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="扣款绑卡渠道" prop="deductionBindCardChannel">
                      <el-select v-model="form.deductionBindCardChannel" clearable size="small" placeholder="请选择资产方" class="filter-item" style="width: 280px;" :disabled="type === 'detail'">
                        <el-option v-for="item in assetList" :key="item.flowChannel" :label="item.flowNameShort" :value="item.flowChannel" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="日放款限额(万元)" prop="dailyLoanLimit">
                      <el-input v-model="form.dailyLoanLimit" style="width: 280px;" :disabled="type === 'detail'"/>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="24">
                  <el-col :span="8">
                    <el-form-item label="单笔限额(元)" prop="drawableAmountStep">
                      <el-input v-model="form.drawableAmountStep" style="width: 280px;" :disabled="type === 'detail'"/>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="是否允许续借" prop="contactPerson">
                      <el-radio-group v-model="form.radio" :disabled="type === 'detail'">
                        <el-radio label="1">是</el-radio>
                        <el-radio label="0">否</el-radio>
                      </el-radio-group>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
            </el-collapse-item>
            <el-collapse-item title="还款" name="5">
              <el-form ref="form" :model="form" :rules="rules" size="small" label-width="120px">
                <el-row :gutter="24">
                  <el-col :span="8">
                    <el-form-item label="逾期宽限期(天)" prop="gracePeriodDays">
                      <el-input v-model="form.gracePeriodDays" style="width: 280px;" :disabled="type === 'detail'"/>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="宽限期类型" prop="gracePeriodType">
                      <el-select v-model="form.gracePeriodType" clearable size="small" placeholder="请选择资产方" class="filter-item" style="width: 280px;" :disabled="type === 'detail'">
                        <el-option v-for="item in assetList" :key="item.flowChannel" :label="item.flowNameShort" :value="item.flowChannel" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="扣款商户号" prop="deductionMerchantCode">
                      <el-input v-model="form.deductionMerchantCode" style="width: 280px;" :disabled="type === 'detail'"/>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="24">
                  <el-col :span="8">
                    <el-form-item label="是否支持对客减免" prop="mainProd">
                      <el-select v-model="form.flowChannel" clearable size="small" placeholder="请选择资产方" class="filter-item" style="width: 280px;" :disabled="type === 'detail'">
                        <el-option v-for="item in assetList" :key="item.flowChannel" :label="item.flowNameShort" :value="item.flowChannel" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="是否支持对资减免" prop="allowCollectionWaiver">
                      <el-select v-model="form.allowCollectionWaiver" clearable size="small" placeholder="请选择资产方" class="filter-item" style="width: 280px;" :disabled="type === 'detail'">
                        <el-option v-for="item in assetList" :key="item.flowChannel" :label="item.flowNameShort" :value="item.flowChannel" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="催收方" prop="collectionParty">
                      <el-select v-model="form.collectionParty" clearable size="small" placeholder="请选择资产方" class="filter-item" style="width: 280px;" :disabled="type === 'detail'">
                        <el-option v-for="item in assetList" :key="item.flowChannel" :label="item.flowNameShort" :value="item.flowChannel" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="24">
                  <el-col :span="8">
                    <el-form-item label="是否推送催收数据" prop="pushCollectionData">
                      <el-radio-group v-model="form.pushCollectionData" :disabled="type === 'detail'">
                        <el-radio label="1">是</el-radio>
                        <el-radio label="0">否</el-radio>
                      </el-radio-group>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
            </el-collapse-item>
          </el-collapse>
          <div style="text-align: center; margin-top: 30px;">
            <el-button @click="goBack">返 回</el-button>
            <el-button type="primary" @click="editProject">保 存</el-button>
          </div>
        </el-tab-pane>
        <el-tab-pane label="对客协议" name="second" >
          <div class="head-container">
            <el-form :model="agreementQuery" ref="queryForm" :inline="true">
              <el-form-item>
                <label class="el-form-item-label">合同模板类型</label>
                <el-input v-model="agreementQuery.contractTemplateType" clearable placeholder="请输入项目编码" style="width: 185px;" class="filter-item" />
              </el-form-item>
              <el-form-item>
                <label class="el-form-item-label">归属方名称</label>
                <el-input v-model="agreementQuery.templateOwnerName" clearable placeholder="请输入项目名称" style="width: 185px;" class="filter-item" />
              </el-form-item>
              <el-form-item>
                <label class="el-form-item-label">状态</label>
                <el-select v-model="agreementQuery.enabled" clearable size="small" placeholder="请选择项目类型" class="filter-item" style="width: 90px" >
                  <el-option v-for="item in ableStatusExt" :key="item.label" :label="item.value" :value="item.label" />
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button round size="mini" type="primary" icon="el-icon-search" @click="search">搜索</el-button>
                <el-button round size="mini" icon="el-icon-refresh-left" @click="reset">重置</el-button>
              </el-form-item>
            </el-form>
            <!--列表按钮-->
            <el-button
              v-permission="permission.add"
              class="filter-item"
              size="mini"
              type="primary"
              icon="el-icon-plus"
              @click="openAgreementDialog('add')"
            >
              新增
            </el-button>
            <el-button
              v-permission="permission.edit"
              class="filter-item"
              size="mini"
              type="success"
              icon="el-icon-edit"
              @click="openAgreementDialog('edit')"
            >
              修改
            </el-button>
            <el-button
              v-permission="permission.dtl"
              class="filter-item"
              size="mini"
              type="info"
              icon="el-icon-document"
              @click="openAgreementDialog('detail')"
            >
              查看
            </el-button>
            <el-button
              v-permission="permission.dtl"
              class="filter-item"
              size="mini"
              type="danger"
              icon="el-icon-document"
              @click="deleteAgreement"
            >
              删除
            </el-button>
            <el-button
              v-permission="permission.dtl"
              class="filter-item"
              size="mini"
              type="danger"
              icon="el-icon-circle-close"
              @click="stopUseAgreement"
            >
              启用/停用
            </el-button>
            <!--表单组件-->
            <el-dialog :visible.sync="agreementDialog" :close-on-click-modal="false" title="新增临时配置项" width="1000px">
              <el-form ref="agreementForm" :model="agreementForm" :rules="rules" size="small" label-width="120px">
                <el-row :gutter="24">
                  <el-col :span="12">
                    <el-form-item label="合同模板编码" prop="templateCode">
                      <el-input v-model="agreementForm.templateCode" style="width: 280px;" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="合同模板类型" prop="contractTemplateType">
                      <el-input v-model="agreementForm.contractTemplateType" style="width: 280px;" />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="24">
                  <el-col :span="12">
                    <el-form-item label="资金方签署阶段" prop="capitalLoanStage">
                      <el-input v-model="agreementForm.capitalLoanStage" style="width: 280px;" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="资产方签署阶段" prop="flowLoanStage">
                      <el-input v-model="agreementForm.flowLoanStage" style="width: 280px;" />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="24">
                  <el-col :span="12">
                    <el-form-item label="归属方" prop="templateOwner">
                      <el-input v-model="agreementForm.templateOwner" style="width: 280px;" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="归属方名称" prop="templateOwnerName">
                      <el-input v-model="agreementForm.templateOwnerName" style="width: 280px;" />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="24">
                  <el-col :span="12">
                    <el-form-item label="资金方合同名称" prop="capitalContractName">
                      <el-input v-model="agreementForm.capitalContractName" style="width: 280px;" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="资产方合同名称" prop="flowContractName">
                      <el-input v-model="agreementForm.flowContractName" style="width: 280px;" />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="24">
                  <el-col :span="12">
                    <el-form-item label="是否融担签章" prop="isRdSignature">
                      <el-input v-model="agreementForm.isRdSignature" style="width: 280px;" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="签章类型" prop="sealType">
                      <el-input v-model="agreementForm.sealType" style="width: 280px;" />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="24">
                  <el-col :span="12">
                    <el-form-item label="签章系统唯一编码" prop="templateNo">
                      <el-input v-model="agreementForm.templateNo" style="width: 280px;" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="是否回传资金方" prop="isReturnToCapital">
                      <el-input v-model="agreementForm.isReturnToCapital" style="width: 280px;" />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="24">
                  <el-col :span="12">
                    <el-form-item label="是否回传流量方" prop="isReturnToFlow">
                      <el-input v-model="agreementForm.isReturnToFlow" style="width: 280px;" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="上传协议" prop="contractDescription">
                      <input type="file" @change="uploadFile" accept=".pdf,.PDF"/>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
              <div slot="footer" class="dialog-footer">
                <el-button @click="agreementCancel">取 消</el-button>
                <el-button type="primary" @click="agreementSubmit">确 认</el-button>
              </div>
            </el-dialog>
            <!--表格渲染-->
            <el-table ref="AgreementTable" border v-loading="agreementLoading" :data="agreementData" size="small" style="width: 100%;" @selection-change="handleAgreementChange">
              <el-table-column type="selection" width="55" />
              <el-table-column type="index" label="序号" width="55" />
              <el-table-column prop="templateCode" label="合同模板编码" />
              <el-table-column prop="contractTemplateType" label="合同模板类型" />
              <el-table-column prop="flowLoanStage" label="资金方签署阶段" />
              <el-table-column prop="capitalLoanStage" label="资产方签署阶段" />
              <el-table-column prop="templateOwner" label="归属方" />
              <el-table-column prop="templateOwnerName" label="归属方名称" />
              <el-table-column prop="capitalContractName" label="资金方合同名称" />
              <el-table-column prop="flowContractName" label="资产方合同名称" />
              <el-table-column prop="isRdSignature" label="是否融担签章" />
              <el-table-column prop="sealType" label="签章类型" />
              <el-table-column prop="templateNo" label="签章系统唯一编码" />
              <el-table-column prop="isReturnToCapital" label="是否回传资金方" />
              <el-table-column prop="isReturnToFlow" label="是否回传流量方" />
              <el-table-column prop="enabled" label="状态"  :formatter = "(row, column, cellValue) => {return ableStatusExt.find(item => item.label === cellValue).value}"/>
            </el-table>
            <!--分页组件-->
            <pagination :total="agreementQuery.total" :page.sync="agreementQuery.pageNum" :limit.sync="agreementQuery.pageSize"
            @pagination="queryAgreementList" />
          </div>
        </el-tab-pane>
        <el-tab-pane label="临时配置项" name="third">
          <!--工具栏-->
          <div class="head-container">
            <!--列表按钮-->
            <el-button
              v-permission="permission.add"
              class="filter-item"
              size="mini"
              type="primary"
              icon="el-icon-plus"
              @click="openConfigDialog('add')"
            >
              新增
            </el-button>
            <el-button
              v-permission="permission.edit"
              class="filter-item"
              size="mini"
              type="success"
              icon="el-icon-edit"
              @click="openConfigDialog('edit')"
            >
              修改
            </el-button>
            <el-button
              v-permission="permission.dtl"
              class="filter-item"
              size="mini"
              type="info"
              icon="el-icon-document"
              @click="openConfigDialog('detail')"
            >
              查看
            </el-button>
            <el-button
              v-permission="permission.dtl"
              class="filter-item"
              size="mini"
              type="danger"
              icon="el-icon-circle-close"
              @click="stopUseConfig"
            >
              启用/停用
            </el-button>
            <!--表单组件-->
            <el-dialog :visible.sync="configDialog" :close-on-click-modal="false" :title="新增临时配置" width="1000px">
              <el-form ref="configForm" :model="configForm" :rules="rules" size="small" label-width="120px">
                <el-row :gutter="24" v-if = "configType !== 'add'">
                  <el-col :span="12">
                    <el-form-item label="产品要素编码" prop="id">
                      <el-input v-model="configForm.id" style="width: 280px;" disabled/>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="24">
                  <el-col :span="12">
                    <el-form-item label="配置有效期起" prop="contractCode">
                      <el-date-picker
                        value-format = "yyyy-MM-dd"
                        v-model="configForm.tempStartTime"
                        align="right"
                        type="date"
                        placeholder="选择日期"
                        style="width: 280px;" :disabled="configType === 'detail'">
                      </el-date-picker>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="配置有效期止" prop="contractCode">
                      <el-date-picker
                        value-format = "yyyy-MM-dd"
                        v-model="configForm.tempEndTime"
                        align="right"
                        type="date"
                        placeholder="选择日期"
                        style="width: 280px;" :disabled="configType === 'detail'">
                      </el-date-picker>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="24">
                  <el-col :span="12">
                    <el-form-item label="对客授信黑暗期" prop="creditDarkHours">
                      <el-time-picker
                        format="HH:mm"
                        is-range
                        v-model="configForm.creditDarkHoursRange"
                        range-separator="-"
                        start-placeholder="开始时间"
                        end-placeholder="结束时间"
                        placeholder="选择时间范围" style="width: 280px;" :disabled="configType === 'detail'">
                      </el-time-picker>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="对客用信黑暗期" prop="loanDarkHours">
                      <el-time-picker
                        format="HH:mm"
                        is-range
                        v-model="configForm.loanDarkHoursRange"
                        range-separator="-"
                        start-placeholder="开始时间"
                        end-placeholder="结束时间"
                        placeholder="选择时间范围" style="width: 280px;" :disabled="configType === 'detail'">
                      </el-time-picker>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="24">
                  <el-col :span="12">
                    <el-form-item label="对客还款黑暗期" prop="repayDarkHours">
                      <el-time-picker
                        format="HH:mm"
                        is-range
                        v-model="configForm.repayDarkHoursRange"
                        range-separator="-"
                        start-placeholder="开始时间"
                        end-placeholder="结束时间"
                        placeholder="选择时间范围" style="width: 280px;" :disabled="configType === 'detail'">
                      </el-time-picker>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="日放款限额(万元)" prop="dailyLoanLimit">
                      <el-input v-model="configForm.dailyLoanLimit" style="width: 280px;" :disabled="configType === 'detail'"/>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
              <div slot="footer" class="dialog-footer">
                <el-button @click="configCancel">取 消</el-button>
                <el-button type="primary" @click="configSubmit">确 认</el-button>
              </div>
            </el-dialog>
            <!--表格渲染-->
            <el-table ref="configTable" border v-loading="configLoading" :data="configData" size="small" style="width: 100%;" @selection-change="handleConfigChange">
              <el-table-column type="selection" width="55" />
              <el-table-column type="index" label="序号" width="55" />
              <el-table-column prop="id" label="产品要素编码" />
              <el-table-column prop="temRemark" label="备注" />
              <el-table-column prop="tempStartTime" label="有效期起" />
              <el-table-column prop="tempEndTime" label="有效期止" />
              <el-table-column prop="temEnabled" label="状态" :formatter = "(row, column, cellValue) => {return ableStatusExt.find(item => item.label === cellValue).value}"/>
            </el-table>
            <!--分页组件-->
            <pagination :total="configQuery.total" :page.sync="configQuery.pageNum" :limit.sync="configQuery.pageSize"
            @pagination="queryConfigList" />
          </div>
        </el-tab-pane>
        <el-tab-pane label="其他配置" name="fourth">

        </el-tab-pane>
      </el-tabs>
    </div>
</template>

<script>
import { delAgreement, upload, addProject, getConfigDtl, addAgreement, updateAgreement, getAgreementDtl, updateTemProject, getAssetList, getGuaranteeList, getFundList, getDetail, updateProject, getAgreementList, getConfigList } from '@/api/projectManage/project'
import { get as getDictByName } from "@/api/system/dictDetail"
import { formatTimeRange } from '@/utils'

export default {
  name: 'ProjectDtl',
  data() {
    return {
      // 查询参数
      agreementQuery: {
        pageNum: 1,
        pageSize: 10,
        total: 0
      },
      configQuery: {
        pageNum: 1,
        pageSize: 10,
        total: 0
      },
      loanTermsOptions: ['3期', '6期', '9期', '12期', '18期', '20期', '24期', '36期'],
      checkedCities: [],
      // 字典
      ableStatusExt: [],
      projectType: [],
      // 选取数据
      selectedRows: {},
      // 弹窗开关
      agreementDialog: false,
      configDialog: false,
      // 资产方选择
      assetList: [],
      // 融担方选择
      guaranteeList: [],
      // 资金方选择
      fundList: [],
      // 新增表单数据
      form: {},
      agreementForm: {},
      configForm: {
        // 初始化时间选择器绑定的字段，确保响应式
        creditDarkHoursRange: null,
        loanDarkHoursRange: null,
        repayDarkHoursRange: null
      },
      configSelect: {},
      agreementSelect: {},
      // 主列表数据
      agreementData: [],
      configData: [],
      // 主列表传入id
      id: undefined,
      type: undefined,
      projectCode: undefined,
      // 加载效果
      agreementLoading: false,
      configLoading: false,
      configType: '',
      agreementType: '',
      // 文件地址
      adder: '',
      // 折叠开关
      activeNames: ['1', '2', '3', '4', '5', '6'],
      // tab选中
      activeName: 'first',
      // 按钮权限
      permission: {
        add: ['admin', 'projectContract:add'],
        edit: ['admin', 'projectContract:edit'],
        del: ['admin', 'projectContract:del']
      },
      // 校验规则
      rules: {
        flowChannel: [
          { required: true, message: '资产方编码不能为空', trigger: 'blur' }
        ],
        flowNameShort: [
          { required: true, message: '资产方简称不能为空', trigger: 'blur' }
        ],
        flowName: [
          { required: true, message: '资产方公司名称不能为空', trigger: 'blur' }
        ],
        mainProd: [
          { required: true, message: '主营产品不能为空', trigger: 'blur' }
        ],
        contractCode: [
          { required: true, message: '资产方接入日期不能为空', trigger: 'blur' }
        ]
      }    }
  },

    // 钩子函数
  // created() {
  //   // 路由更新时执行
  //   // 获取列表传参
  //   this.id = this.$route.query.id;
  //   this.type = this.$route.query.type;
  //   this.projectCode = this.$route.query.projectCode;
  //   // 获取项目状态字典
  //   getDictByName("ableStatusExt").then(res => {
  //     this.ableStatusExt = res.content;
  //   });
  //   // 获取项目类型字典
  //   getDictByName("projectType").then(res => {
  //     this.projectType = res.content;
  //   });
  //   this.queryAsset({pageNum: 1, pageSize: 99999,});
  //   this.queryGuarantee({pageNum: 1, pageSize: 99999,});
  //   this.queryFund({pageNum: 1, pageSize: 99999,});
  //   // 查询详情
  //   this.queryDetail({id: this.id});
  //   // 查询临时配置项列表
  //   this.queryConfigList(this.configQuery);
  //   this.queryAgreementList(this.agreementQuery)
  // },

  activated() {
    // 路由更新时执行
    // 获取列表传参
    this.id = this.$route.query.id;
    this.type = this.$route.query.type;
    // 获取项目状态字典
    getDictByName("ableStatusExt").then(res => {
      this.ableStatusExt = res.content;
    });
    // 获取项目类型字典
    getDictByName("projectType").then(res => {
      this.projectType = res.content;
    });
    this.queryAsset({pageNum: 1, pageSize: 99999,});
    this.queryGuarantee({pageNum: 1, pageSize: 99999,});
    this.queryFund({pageNum: 1, pageSize: 99999,});
    // 查询详情
    this.queryDetail({id: this.id});
    // 查询临时配置项列表
    this.queryConfigList(this.configQuery);
    this.queryAgreementList(this.agreementQuery)
  },

  methods: {

    // 查询项目详情
    queryDetail(params) {
      getDetail(params).then((res) => {
        if(res.code === '000000') {
          this.form = res.data;
        };
      })
    },

    // 查询临时配置项详情
    queryConfigDtl(params) {
      params.id = this.configSelect.temId;
      getConfigDtl(params).then((res) => {
        if(res.code === '000000') {
          this.configForm = res.data;
          // 将字符串格式的时间范围转换为时间选择器需要的数组格式
          this.parseTimeRangeForDisplay();
        };
      })
    },

    // 查询协议详情
    queryAgreementDtl(params) {
      params.id = this.agreementSelect.id;
      getAgreementDtl(params).then((res) => {
        if(res.code === '000000') {
          this.agreementForm = res.data;
        };
      })
    },

    // 查询对客协议列表
    queryAgreementList(params) {
      this.agreementLoading = true;
      params.projectCode = this.projectCode
      getAgreementList(params).then((res) => {
        if(res.code === '000000') {
          this.agreementData = res.data.content;
          this.agreementQuery.total = res.data.totalElements;
        };
        this.agreementLoading = false;
      })
    },

    handleTimeChange() {
      // 使用公共工具方法格式化时间范围
      if(this.configForm.creditDarkHours && this.configForm.creditDarkHours.length === 2){
        this.configForm.creditDarkHours = formatTimeRange(this.configForm.creditDarkHours)
      }
      if(this.configForm.loanDarkHours && this.configForm.loanDarkHours.length === 2){
        this.configForm.loanDarkHours = formatTimeRange(this.configForm.loanDarkHours)
      }
      if(this.configForm.repayDarkHours && this.configForm.repayDarkHours.length === 2){
        this.configForm.repayDarkHours = formatTimeRange(this.configForm.repayDarkHours)
      }
    },

    // 将字符串格式的时间范围转换为时间选择器需要的数组格式（用于回显）
    parseTimeRangeForDisplay() {
      const parseTimeString = (timeStr) => {
        if (!timeStr || typeof timeStr !== 'string') return null;
        const times = timeStr.split('-');
        if (times.length !== 2) return null;

        const today = new Date();
        const parseTime = (time) => {
          const [hours, minutes] = time.split(':');
          const date = new Date(today);
          date.setHours(parseInt(hours), parseInt(minutes), 0, 0);
          return date;
        };

        return [parseTime(times[0]), parseTime(times[1])];
      };

      if (this.configForm.creditDarkHours) {
        this.configForm.creditDarkHoursRange = parseTimeString(this.configForm.creditDarkHours);
      }
      if (this.configForm.loanDarkHours) {
        this.configForm.loanDarkHoursRange = parseTimeString(this.configForm.loanDarkHours);
      }
      if (this.configForm.repayDarkHours) {
        this.configForm.repayDarkHoursRange = parseTimeString(this.configForm.repayDarkHours);
      }
    },

    // 将时间选择器的数组格式转换为字符串格式（用于提交）
    formatTimeRangeForSubmit() {
      if (this.configForm.creditDarkHoursRange && this.configForm.creditDarkHoursRange.length === 2) {
        this.configForm.creditDarkHours = formatTimeRange(this.configForm.creditDarkHoursRange);
      }
      if (this.configForm.loanDarkHoursRange && this.configForm.loanDarkHoursRange.length === 2) {
        this.configForm.loanDarkHours = formatTimeRange(this.configForm.loanDarkHoursRange);
      }
      if (this.configForm.repayDarkHoursRange && this.configForm.repayDarkHoursRange.length === 2) {
        this.configForm.repayDarkHours = formatTimeRange(this.configForm.repayDarkHoursRange);
      }
    },

    // 查询临时配置项列表
    queryConfigList(params) {
      this.configLoading = true;
      params.projectCode = this.projectCode
      getConfigList(params).then((res) => {
        if(res.code === '000000') {
          this.configData = res.data.content;
          this.configQuery.total = res.data.totalElements;
        };
        this.configLoading = false;
      })
    },

    // 查询资产方主列表
    queryAsset(params) {
      getAssetList(params).then((res) => {
        if(res.code === '000000') {
          this.assetList = res.data.content;
        };
      })
    },

    // 查询融担方主列表
    queryGuarantee(params) {
      getGuaranteeList(params).then((res) => {
        if(res.code === '000000') {
          this.guaranteeList = res.data.content;
        };
      })
    },

    // 查询资金方主列表
    queryFund(params) {
      getFundList(params).then((res) => {
        if(res.code === '000000') {
          this.fundList = res.data.content;
        };
      })
    },

    // 修改项目
    editProject() {
      updateProject(this.form).then((res) => {
        if(res.code === '000000') {
          this.goBack();
          this.$message({
            message: '项目修改成功!',
            type: 'success'
          });
        } else {
          this.$message({
            message: '项目修改失败!',
            type: 'error'
          });
        }
      })
    },

    handleCheckedCitiesChange(value) {
      let checkedCount = value.length;
      this.checkAll = checkedCount === this.cities.length;
      this.isIndeterminate = checkedCount > 0 && checkedCount < this.cities.length;
    },

    goBack() {
      this.$router.back();
    },

    configCancel() {
      this.configDialog = false;
      this.configForm = {
        // 重置时间选择器绑定的字段
        creditDarkHoursRange: null,
        loanDarkHoursRange: null,
        repayDarkHoursRange: null
      };
    },

    agreementCancel() {
      this.agreementDialog = false;
      this.agreementForm = {};
    },

    uploadFile(e) {
      let file = e.target.files[0];
      let fordata = new FormData();
      fordata.append('file', file);
      upload(fordata).then((res) => {
        if(res.code === '000000') {
          this.adder = res.data;
          this.$message({
            message: '合同上传成功!',
            type: 'success'
          });
        } else {
          this.$message({
            message: '合同上传失败!',
            type: 'error'
          });
        }
      })
    },

    // 新增临时配置项
    configSubmit() {
      // 在提交前将时间选择器的数组格式转换为字符串格式
      this.formatTimeRangeForSubmit();

      if(this.configType === 'add') {
        this.configForm.projectCode = this.projectCode;
        let element = {
          ...this.configForm,
          projectDurationType: 'TEMPORARY'
        };
        this.configForm.elements = element;
        let params = this.configForm;
        addProject(params).then((res) => {
          if(res.code === '000000') {
            this.configDialog = false;
            this.configForm = {
              creditDarkHoursRange: null,
              loanDarkHoursRange: null,
              repayDarkHoursRange: null
            };
            this.queryConfigList(this.configQuery);
            this.$message({
              message: '临时配置项新增成功!',
              type: 'success'
            });
          } else {
            this.$message({
              message: '临时配置项新增失败!',
              type: 'error'
            });
          }
        })
      } else {
        this.configForm.id = this.configSelect.temId;
        updateTemProject(this.configForm).then((res) => {
          if(res.code === '000000') {
            this.configDialog = false;
            this.configForm = {};
            this.queryConfigList(this.configQuery);
            this.$message({
              message: '临时项目修改成功!',
              type: 'success'
            });
          } else {
            this.$message({
              message: '临时项目修改失败!',
              type: 'error'
            });
          }
        })
      }
    },

    // 新增协议
    agreementSubmit() {
      if(this.agreementType === 'add') {
        this.agreementForm.projectCode = this.projectCode;
        this.agreementForm.filePath = this.adder;
        this.agreementForm.contractTemplateType = {contractTemplateType:this.agreementForm.contractTemplateType}
        addAgreement(this.agreementForm).then((res) => {
          if(res.code === '000000') {
            this.agreementDialog = false;
            this.agreementForm = {};
            this.queryAgreementList(this.agreementQuery);
            this.$message({
              message: '对客协议新增成功!',
              type: 'success'
            });
          } else {
            this.$message({
              message: '对客协议新增失败!',
              type: 'error'
            });
          }
        })
      } else {
        this.agreementForm.id = this.agreementSelect.id;
        updateAgreement(this.agreementForm).then((res) => {
          if(res.code === '000000') {
            this.agreementDialog = false;
            this.agreementForm = {};
            this.queryAgreementList(this.configQuery);
            this.$message({
              message: '对客协议修改成功!',
              type: 'success'
            });
          } else {
            this.$message({
              message: '对客协议修改失败!',
              type: 'error'
            });
          }
        })
      }
    },

    // 启用/停用临时配置
    stopUseConfig() {
      if (Object.keys(this.configSelect).length !== 0) {
        this.$confirm("确定启用/停用当前项目？","警告",{type:"warning"}).then(()=>{
          const params = {
            id: this.configSelect.temId,
            enabled: this.configSelect.enabled === 'INIT' ? 'ENABLE' : this.configSelect.enabled === 'ENABLE' ? 'DISABLE' : 'ENABLE'
          }
          updateTemProject(params).then((res) => {
            if(res.code === '000000') {
              this.queryConfigList(this.configQuery);
              this.$message({
                message: '项目启用/停用成功!',
                type: 'success'
              });
            } else {
              this.$message({
                message: '项目启用/停用失败!',
                type: 'error'
              });
            }
          })
        }).catch(()=>{

        });
      } else {
        this.$message({
          message: '请选择一个项目!',
          type: 'warning'
        });
      }
    },

    // 启用/停用协议
    stopUseAgreement() {
      if (Object.keys(this.agreementSelect).length !== 0) {
        this.$confirm("确定启用/停用当前协议？","警告",{type:"warning"}).then(()=>{
          const params = {
            id: this.agreementSelect.id,
            enabled: this.agreementSelect.enabled === 'INIT' ? 'ENABLE' : this.configSelect.enabled === 'ENABLE' ? 'DISABLE' : 'ENABLE'
          }
          updateAgreement(params).then((res) => {
            if(res.code === '000000') {
              this.queryAgreementList(this.agreementQuery);
              this.$message({
                message: '协议启用/停用成功!',
                type: 'success'
              });
            } else {
              this.$message({
                message: '协议启用/停用失败!',
                type: 'error'
              });
            }
          })
        }).catch(()=>{

        });
      } else {
        this.$message({
          message: '请选择一个项目!',
          type: 'warning'
        });
      }
    },

    // 打开临时配置项弹窗
    openConfigDialog(type) {
      if(type !== 'add') {
        if (Object.keys(this.configSelect).length !== 0) {
          this.queryConfigDtl(this.configQuery)
          this.configType = type;
          this.configDialog = true;
        } else {
          this.$message({
            message: '请选择一个项目!',
            type: 'warning'
          });
        }
      } else {
          this.configType = type;
          this.configDialog = true;
      }
    },

    openAgreementDialog(type) {
      if(type !== 'add') {
        if (Object.keys(this.agreementSelect).length !== 0) {
          this.queryAgreementDtl(this.agreementQuery)
          this.agreementType = type;
          this.agreementDialog = true;
        } else {
          this.$message({
            message: '请选择一个项目!',
            type: 'warning'
          });
        }
      } else {
          this.agreementType = type;
          this.agreementDialog = true;
      }
    },

    // 搜索对客协议
    search() {
      this.queryAgreementList(this.agreementQuery);
    },

    // 重置搜索条件
    reset() {
      this.agreementQuery = {
        pageNum: 1,
        pageSize: 10,
        total: 0
      }
      this.queryAgreementList(this.agreementQuery);
    },

    // 控制单选
    handleConfigChange(val) {
      if (val.length > 1) {
        this.$refs.configTable.clearSelection();
        this.$refs.configTable.toggleRowSelection(val[val.length - 1]);
      }
      this.configSelect = val[val.length - 1];
      if(!this.configSelect) {
        this.configSelect = {}
      }
    },
    // 控制单选
    handleAgreementChange(val) {
      if (val.length > 1) {
        this.$refs.AgreementTable.clearSelection();
        this.$refs.AgreementTable.toggleRowSelection(val[val.length - 1]);
      }
      this.agreementSelect = val[val.length - 1];
      if(!this.agreementSelect) {
        this.agreementSelect = {}
      }
    },

    // 删除协议
    deleteAgreement() {
      delAgreement([{id: this.agreementSelect.id}]).then((res) => {
        if(res.code === '000000') {
          this.queryAgreementList(this.agreementQuery);
          this.$message({
            message: '项目删除成功!',
            type: 'success'
          });
        } else {
          this.$message({
            message: '项目删除失败!',
            type: 'error'
          });
        }
      })
    },

  }
}
</script>

<style scoped>
::v-deep .el-collapse-item__header{
  font-size: 18px;
  font-weight: 600;
}
::v-deep .el-table__header-wrapper .el-checkbox {
  display: none;
}
</style>
